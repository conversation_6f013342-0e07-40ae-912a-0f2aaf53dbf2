import sys
import os
import pandas as pd
from pathlib import Path
import numpy as np
import pyodbc
sys.path.append(os.path.dirname(Path.cwd()))
import Get_System_Data_SQL as gsd
from datetime import datetime
import Replenishment_Model_Functions_25 as rmf
import polars as pl
import re
import json
import paramiko
import time
from datetime import datetime
from tqdm import tqdm
import colorama
from colorama import Fore, Back, Style
import csv


start = '20250630'
end = '20250630'






try:
    # Try to use __file__ to get the script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
except NameError:
    # Fallback to using the current working directory
    script_dir = os.getcwd()

# Construct the path to the config file one level up from the script directory or current working directory
config_path = os.path.join(script_dir, os.pardir, 'config.json')

# Load the configuration from the JSON file
with open(config_path, 'r') as file:
    config = json.load(file)

# Extract the necessary details from the configuration
hostname = config['SSH_hostname']
username = config['SSH_username']
password = config['SSH_password']


ODBC_CONN = config['ODBC_connection']






        
        
def ssh_table_create(what_to_create, start, end, pmg, nr_weeks, wp_working_output, saved_name):
            
    if what_to_create == "cases_delivered":
        
        success = run_cases_delivered_script(hostname, password)
        
        if success:
            flag = 0
            print("=" * 50)
            print("🎉 All done! Your cases_delivered table is ready.")
        else:
            print("💥 Something went wrong. Check the server logs.")
            print("=" * 50)
        
    else:
    
        # Setup the connection
        try:
            ssh_client = paramiko.SSHClient()
            ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            ssh_client.connect(hostname=hostname, username="phrubos", password=password)
            print("\nConnection is done\n")
        except:
            print("\nNo connection!\n")  
        
        # Setup FTP client
        ftp_client = ssh_client.open_sftp()      
            
        ftp_client.get(f"/home/<USER>/{what_to_create}/{what_to_create}_create_table.sql", wp_working_output / saved_name / f"{what_to_create}_create_table.sql")
        
        file_path = wp_working_output / saved_name /  f"{what_to_create}_create_table.sql"
        
        start = start.strip("'")
        end = end.strip("'")
        
        # parameter the SQL file
        with open(file_path, 'r') as file:
            sql_content = file.read()
    
            start_pattern = r"(?<=BETWEEN\s)'f(\d{4}w\d{2})'"
            end_pattern = r"(?<=AND\s)'f(\d{4}w\d{2})'"
            
            modified_content = re.sub(start_pattern, f"'{start}'", sql_content)
            modified_content = re.sub(end_pattern, f"'{end}'", modified_content)
            
            
            # weeks_pattern = r"/\d+\sAS"
    
            # modified_content = re.sub(weeks_pattern, f"/{nr_weeks} AS", modified_content)
            
            if pmg != None:
            
                # New pattern to replace PMG values
                pmg_pattern = r"WHERE SUBSTRING\(pmg, 1, 3\) IN \([^)]+\)"
                modified_content = re.sub(pmg_pattern, f"WHERE SUBSTRING(pmg, 1, 3) IN {pmg}", modified_content)
            
    
    
        with open(file_path, 'w') as file:
            file.write(modified_content)
            
            
        ftp_client.put(wp_working_output / saved_name / f"{what_to_create}_create_table.sql", f"/home/<USER>/{what_to_create}/{what_to_create}_create_table.sql")
        
    
        
        print(f"\nSQL file Modification complete. Saved to\n{file_path}\n", )
            
            
        print(f"\nScript ({what_to_create}) is being started.....\n")
            
        stdin, stdout, stderr = ssh_client.exec_command(f"sh /home/<USER>/{what_to_create}/start_q")
        
        exit_status = stdout.channel.recv_exit_status()
        
        flag = 0
        if exit_status == 0:
            print(f"\nScript ({what_to_create}) finished successfully.\n")
        else:
            print(f"Script failed with an error:\n{stderr.read().decode('utf-8')}")
            flag+=1
            
        ssh_client.close()
    
    return flag

        def run_cases_delivered_script(hostname, password, timeout_minutes=90):
            """
            Simple SSH monitor for cases_delivered SQL script execution
            """
            
            print("=" * 50)
            print("🏪 Cases_delivered Analysis Script Monitor")
            print("=" * 50)
            
            
            
            print(f"🔌 Connecting to {hostname}...")
            
            ssh_client = paramiko.SSHClient()
            ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            try:
                # Connect
                ssh_client.connect(hostname=hostname, username="phrubos", password=password, timeout=30)
                print("✅ Connected successfully")
                
                # Quick file check
                stdin, stdout, stderr = ssh_client.exec_command("ls -la /home/<USER>/cases_delivered_full/unit_cases_delivered_prod.sql")
                if 'unit_cases_delivered_prod.sql' not in stdout.read().decode('utf-8'):
                    print("❌ SQL file not found!")
                    return False
                
                # Start execution
                print(f"🚀 Starting cases_delivered script execution...")
                start_time = time.time()
                
                stdin, stdout, stderr = ssh_client.exec_command("sh /home/<USER>/cases_delivered_full/start_q 2>&1", get_pty=True)
                
                # Monitor progress
                last_update = time.time()
                while True:
                    if stdout.channel.exit_status_ready():
                        break
                        
                    if stdout.channel.recv_ready():
                        chunk = stdout.channel.recv(1024).decode('utf-8', errors='ignore')
                        if chunk:
                            last_update = time.time()
                            
                            # Show only important progress
                            for line in chunk.split('\n'):
                                line = line.strip()
                                if not line:
                                    continue
                                    
                                # Key progress indicators
                                if 'CREATE TABLE' in line.upper():
                                    print("📋 Creating table...")
                                elif 'DROP TABLE' in line.upper():
                                    print("🗑️  Dropping old table...")
                                elif 'SELECT DISTINCT' in line.upper():
                                    print("🔍 Processing data...")
                                elif 'Job' in line and 'finished' in line:
                                    print("✨ Stage completed")
                                elif 'Exception' in line or 'ERROR' in line.upper():
                                    print(f"⚠️  Warning: {line[:80]}...")
                    
                    # Timeout check
                    elapsed = time.time() - start_time
                    idle_time = time.time() - last_update
                    
                    if elapsed > (timeout_minutes * 60):
                        print(f"⏰ Timeout after {timeout_minutes} minutes")
                        return False
                        
                    if idle_time > 60:  # Show progress every minute of silence
                        print(f"⏳ Still running... ({elapsed/60:.1f} minutes elapsed)")
                        last_update = time.time()
                        
                    time.sleep(2)
                
                # Check result
                exit_status = stdout.channel.recv_exit_status()
                total_time = time.time() - start_time
                
                print(f"⏱️  Execution time: {total_time/60:.1f} minutes")
                
                if exit_status == 0:
                    print("✅ Script completed successfully!")
                    
                    # Quick verification
                    print("🔍 Verifying table creation...")
                    cmd = "echo 'SELECT COUNT(*) FROM sch_analysts.tbl_cases_delivered_productivity;' | /opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file -u '******************************************************/' --silent=true"
                    
                    stdin, stdout, stderr = ssh_client.exec_command(cmd, timeout=60)
                    output = stdout.read().decode('utf-8')
                    
                    # Extract row count (look for numbers in the output)
                    import re
                    numbers = re.findall(r'\b\d{6,}\b', output)  # Look for 6+ digit numbers
                    if numbers:
                        row_count = int(numbers[-1])  # Take the last large number found
                        print(f"📊 Table created with {row_count:,} rows")
                    else:
                        print("✅ Table created successfully!")
                        
                    return True
                else:
                    print(f"❌ Script failed with exit code: {exit_status}")
                    return False
                    
            except paramiko.AuthenticationException:
                print("❌ Authentication failed - check credentials")
                return False
            except Exception as e:
                print(f"❌ Error: {str(e)}")
                return False
            finally:
                ssh_client.close()
                print("🔌 Connection closed")


        
sql = ssh_table_create("cases_delivered", None, None, None, None, place_to_save, saved_filename)   
        


        